"use client";

import { useEffect, useState } from "react";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
import {
  <PERSON><PERSON>,
  TextField,
  Select,
  Badge,
  Card,
  Tabs,
  Dialog
} from "@radix-ui/themes";
import { Plus, Search, Filter, Code, Users, Eye, Lock } from "lucide-react";
import { RuleCard } from "@/components/rule-card";
import { RuleEditor } from "@/components/rule-editor";
import { Rule, Tag, RulePayload } from "@/lib/store";
import { toast } from "sonner";

// Mock session for testing
const mockSession = {
  user: {
    id: "test-user-id",
    name: "Test User",
    email: "<EMAIL>",
    image: null
  }
};

export default function DashboardTestPage() {
  const session = mockSession; // Use mock session for testing
  const [rules, setRules] = useState<Rule[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState("ALL");
  const [showEditor, setShowEditor] = useState(false);
  const [editingRule, setEditingRule] = useState<Rule | null>(null);

  // Mock data for testing
  const mockRules: Rule[] = [
    {
      id: "1",
      title: "React Component Best Practices",
      description: "Guidelines for writing clean, maintainable React components with TypeScript",
      content: "// React component best practices...",
      ideType: "CURSOR",
      visibility: "PUBLIC",
      userId: "test-user-id",
      shareToken: "abc123",
      createdAt: new Date("2024-01-15T10:30:00Z"),
      updatedAt: new Date("2024-01-15T10:30:00Z"),
      tags: [
        { tag: { id: "1", name: "react", color: "#61dafb" } },
        { tag: { id: "2", name: "typescript", color: "#3178c6" } }
      ],
      user: {
        id: "test-user-id",
        name: "Test User",
        email: "<EMAIL>"
      }
    },
    {
      id: "2", 
      title: "API Route Optimization",
      description: "Best practices for creating efficient and secure API routes in Next.js applications",
      content: "// API optimization patterns...",
      ideType: "AUGMENT",
      visibility: "PRIVATE",
      userId: "test-user-id",
      shareToken: null,
      createdAt: new Date("2024-01-14T15:45:00Z"),
      updatedAt: new Date("2024-01-14T15:45:00Z"),
      tags: [
        { tag: { id: "3", name: "nextjs", color: "#000000" } },
        { tag: { id: "5", name: "api", color: "#10b981" } }
      ],
      user: {
        id: "test-user-id",
        name: "Test User", 
        email: "<EMAIL>"
      }
    }
  ];

  const mockTags: Tag[] = [
    { id: "1", name: "react", color: "#61dafb" },
    { id: "2", name: "typescript", color: "#3178c6" },
    { id: "3", name: "nextjs", color: "#000000" },
    { id: "4", name: "tailwind", color: "#06b6d4" },
    { id: "5", name: "api", color: "#10b981" },
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setRules(mockRules);
      setTags(mockTags);
      setLoading(false);
    }, 500);
  }, []);

  const handleCreateRule = () => {
    setEditingRule(null);
    setShowEditor(true);
  };

  const handleEditRule = (rule: Rule) => {
    setEditingRule(rule);
    setShowEditor(true);
  };

  const handleDeleteRule = async (ruleId: string) => {
    setRules(prev => prev.filter(r => r.id !== ruleId));
    toast.success("Rule deleted successfully");
  };

  const handleSaveRule = async (ruleData: RulePayload) => {
    if (editingRule) {
      // Update existing rule
      setRules(prev => prev.map(r => 
        r.id === editingRule.id 
          ? { ...r, ...ruleData, updatedAt: new Date() }
          : r
      ));
      toast.success("Rule updated successfully");
    } else {
      // Create new rule
      const newRule: Rule = {
        id: Date.now().toString(),
        ...ruleData,
        userId: session.user.id,
        shareToken: ruleData.visibility === 'PUBLIC' ? crypto.randomUUID() : null,
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        user: session.user
      };
      setRules(prev => [newRule, ...prev]);
      toast.success("Rule created successfully");
    }
    setShowEditor(false);
    setEditingRule(null);
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev => 
      prev.includes(tagName) 
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  const filteredRules = rules.filter(rule => {
    const matchesSearch = !searchQuery || 
      rule.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tag => rule.tags.some(ruleTag => ruleTag.tag.name === tag));
    
    const matchesIDE = selectedIDE === "ALL" || rule.ideType === selectedIDE;
    
    return matchesSearch && matchesTags && matchesIDE;
  });

  const userRules = Array.isArray(rules) ? rules.filter(rule => rule.userId === session?.user?.id) : [];
  const publicRules = Array.isArray(rules) ? rules.filter(rule => rule.visibility === "PUBLIC") : [];

  const stats = {
    totalRules: userRules.length,
    publicRules: userRules.filter(r => r.visibility === "PUBLIC").length,
    privateRules: userRules.filter(r => r.visibility === "PRIVATE").length,
    totalViews: 1247, // Mock data
  };

  return (
    <div className="container py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard (Test Mode)</h1>
          <p className="text-muted-foreground mt-1">
            Testing text readability improvements on the real dashboard layout
          </p>
        </div>
        <Button onClick={handleCreateRule}>
          <Plus className="mr-2 h-4 w-4" />
          New Rule
        </Button>
      </div>

      {/* Notice */}
      <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
            <p className="text-sm font-medium" style={{ color: 'hsl(30 100% 20%)' }}>
              This is the actual dashboard layout with authentication bypassed for testing.
              All text readability improvements should be visible here.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="border-border" style={{ backgroundColor: 'hsl(0 0% 8%)', borderColor: 'hsl(0 0% 20%)' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(0 0% 98%)' }}>Total Rules</CardTitle>
            <Code className="h-4 w-4" style={{ color: 'hsl(0 0% 75%)' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: 'hsl(0 0% 98%)' }}>{stats.totalRules}</div>
          </CardContent>
        </Card>

        <Card className="border-border" style={{ backgroundColor: 'hsl(0 0% 8%)', borderColor: 'hsl(0 0% 20%)' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(0 0% 98%)' }}>Public Rules</CardTitle>
            <Users className="h-4 w-4" style={{ color: 'hsl(0 0% 75%)' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: 'hsl(0 0% 98%)' }}>{stats.publicRules}</div>
          </CardContent>
        </Card>

        <Card className="border-border" style={{ backgroundColor: 'hsl(0 0% 8%)', borderColor: 'hsl(0 0% 20%)' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(0 0% 98%)' }}>Private Rules</CardTitle>
            <Lock className="h-4 w-4" style={{ color: 'hsl(0 0% 75%)' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: 'hsl(0 0% 98%)' }}>{stats.privateRules}</div>
          </CardContent>
        </Card>

        <Card className="border-border" style={{ backgroundColor: 'hsl(0 0% 8%)', borderColor: 'hsl(0 0% 20%)' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(0 0% 98%)' }}>Total Views</CardTitle>
            <Eye className="h-4 w-4" style={{ color: 'hsl(0 0% 75%)' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: 'hsl(0 0% 98%)' }}>{stats.totalViews}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedIDE} onValueChange={setSelectedIDE}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="IDE Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All IDEs</SelectItem>
            <SelectItem value="GENERAL">General</SelectItem>
            <SelectItem value="CURSOR">Cursor</SelectItem>
            <SelectItem value="AUGMENT">Augment Code</SelectItem>
            <SelectItem value="WINDSURF">Windsurf</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tag Filters */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-foreground" />
          <span className="text-sm font-medium text-foreground">Filter by tags:</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Badge
              key={tag.id}
              variant={selectedTags.includes(tag.name) ? "solid" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleTag(tag.name)}
              style={{
                borderColor: tag.color,
                backgroundColor: selectedTags.includes(tag.name) ? tag.color : "transparent",
              }}
            >
              {tag.name}
            </Badge>
          ))}
        </div>
      </div>

      {/* Content */}
      <Tabs defaultValue="my-rules" className="space-y-6">
        <TabsList>
          <TabsTrigger value="my-rules">My Rules ({userRules.length})</TabsTrigger>
          <TabsTrigger value="community">Community ({publicRules.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-rules" className="space-y-6">
          {userRules.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No rules yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first AI prompt rule to get started
                </p>
                <Button onClick={handleCreateRule}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Rule
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {userRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  onEdit={handleEditRule}
                  onDelete={handleDeleteRule}
                  isOwner={true}
                />
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="community" className="space-y-6">
          {publicRules.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No community rules found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters or check back later
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {publicRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  isOwner={rule.userId === session?.user?.id}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Rule Editor Dialog */}
      <Dialog.Root open={showEditor} onOpenChange={setShowEditor}>
        <Dialog.Content className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <Dialog.Title>
            {editingRule ? "Edit Rule" : "Create New Rule"}
          </Dialog.Title>
          <RuleEditor
            rule={editingRule}
            tags={tags}
            onSave={handleSaveRule}
            onCancel={() => setShowEditor(false)}
          />
        </Dialog.Content>
      </Dialog.Root>
    </div>
  );
}
