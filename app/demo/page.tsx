"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  TextField,
  Select,
  Badge,
  Card,
  Tabs
} from "@radix-ui/themes";
import { Plus, Search, Filter, Code, Users, Eye, Lock, Star, ExternalLink } from "lucide-react";

// Sample data for demonstration
const sampleStats = {
  totalRules: 42,
  publicRules: 28,
  privateRules: 14,
  totalViews: 1247,
};

const sampleTags = [
  { id: "1", name: "react", color: "#61dafb" },
  { id: "2", name: "typescript", color: "#3178c6" },
  { id: "3", name: "nextjs", color: "#000000" },
  { id: "4", name: "tailwind", color: "#06b6d4" },
  { id: "5", name: "api", color: "#10b981" },
];

const sampleRules = [
  {
    id: "1",
    title: "React Component Best Practices",
    description: "Guidelines for writing clean, maintainable React components with TypeScript",
    ideType: "CURSOR",
    visibility: "PUBLIC",
    tags: [
      { tag: { id: "1", name: "react", color: "#61dafb" } },
      { tag: { id: "2", name: "typescript", color: "#3178c6" } }
    ],
    updatedAt: "2024-01-15T10:30:00Z",
    author: "Demo User"
  },
  {
    id: "2",
    title: "API Route Optimization",
    description: "Best practices for creating efficient and secure API routes in Next.js applications",
    ideType: "AUGMENT",
    visibility: "PUBLIC",
    tags: [
      { tag: { id: "3", name: "nextjs", color: "#000000" } },
      { tag: { id: "5", name: "api", color: "#10b981" } }
    ],
    updatedAt: "2024-01-14T15:45:00Z",
    author: "Demo User"
  },
  {
    id: "3",
    title: "Tailwind CSS Utilities",
    description: "Custom utility classes and responsive design patterns for modern web applications",
    ideType: "WINDSURF",
    visibility: "PRIVATE",
    tags: [
      { tag: { id: "4", name: "tailwind", color: "#06b6d4" } }
    ],
    updatedAt: "2024-01-13T09:20:00Z",
    author: "Demo User"
  }
];

function getIDEBadgeColor(ideType: string) {
  const colors = {
    CURSOR: "bg-blue-600",
    AUGMENT: "bg-purple-600", 
    WINDSURF: "bg-green-600",
    GENERAL: "bg-gray-600",
  };
  return colors[ideType as keyof typeof colors] || "bg-gray-600";
}

export default function DemoPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState("ALL");

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev => 
      prev.includes(tagName) 
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  const filteredRules = sampleRules.filter(rule => {
    const matchesSearch = !searchQuery || 
      rule.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tag => rule.tags.some(ruleTag => ruleTag.tag.name === tag));
    
    const matchesIDE = selectedIDE === "ALL" || rule.ideType === selectedIDE;
    
    return matchesSearch && matchesTags && matchesIDE;
  });

  return (
    <div className="container py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard Demo</h1>
          <p className="text-muted-foreground mt-1">
            Demonstrating improved text readability and visual hierarchy
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Rule
        </Button>
      </div>

      {/* Notice */}
      <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20 pt-6">
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
          <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
            This is a demonstration page showing the improved text readability and visual hierarchy fixes.
            All text should now be clearly visible with proper contrast ratios.
          </p>
        </div>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="border-border">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="text-sm font-medium text-card-foreground">Total Rules</div>
            <Code className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{sampleStats.totalRules}</div>
        </Card>

        <Card className="border-border">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="text-sm font-medium text-card-foreground">Public Rules</div>
            <Users className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{sampleStats.publicRules}</div>
        </Card>

        <Card className="border-border">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="text-sm font-medium text-card-foreground">Private Rules</div>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{sampleStats.privateRules}</div>
        </Card>

        <Card className="border-border">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="text-sm font-medium text-card-foreground">Total Views</div>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{sampleStats.totalViews}</div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <TextField.Root
            placeholder="Search rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select.Root value={selectedIDE} onValueChange={setSelectedIDE}>
          <Select.Trigger className="w-full md:w-48" placeholder="IDE Type" />
          <Select.Content>
            <Select.Item value="ALL">All IDEs</Select.Item>
            <Select.Item value="GENERAL">General</Select.Item>
            <Select.Item value="CURSOR">Cursor</Select.Item>
            <Select.Item value="AUGMENT">Augment Code</Select.Item>
            <Select.Item value="WINDSURF">Windsurf</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      {/* Tag Filters */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-foreground" />
          <span className="text-sm font-medium text-foreground">Filter by tags:</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {sampleTags.map((tag) => (
            <Badge
              key={tag.id}
              variant={selectedTags.includes(tag.name) ? "solid" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleTag(tag.name)}
              style={{
                borderColor: tag.color,
                backgroundColor: selectedTags.includes(tag.name) ? tag.color : "transparent",
              }}
            >
              {tag.name}
            </Badge>
          ))}
        </div>
      </div>

      {/* Content */}
      <Tabs.Root defaultValue="demo-rules" className="space-y-6">
        <Tabs.List>
          <Tabs.Trigger value="demo-rules">Demo Rules ({filteredRules.length})</Tabs.Trigger>
          <Tabs.Trigger value="about">About This Demo</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="demo-rules" className="space-y-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRules.map((rule) => (
              <Card key={rule.id} className="group hover:shadow-md transition-all duration-200">
                <div className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="text-lg cursor-pointer hover:text-primary transition-colors inline-flex items-center gap-1">
                        {rule.title}
                        <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-50" />
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {rule.description}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="soft"
                      className={`${getIDEBadgeColor(rule.ideType)} text-white`}
                    >
                      <Code className="mr-1 h-3 w-3" />
                      {rule.ideType}
                    </Badge>
                    {rule.visibility === "PUBLIC" && (
                      <Badge variant="outline">Public</Badge>
                    )}
                  </div>
                  
                  {rule.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {rule.tags.map((ruleTag) => (
                        <Badge
                          key={ruleTag.tag.id}
                          variant="outline"
                          style={{ borderColor: ruleTag.tag.color }}
                          className="text-xs"
                        >
                          {ruleTag.tag.name}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <div className="text-xs text-muted-foreground">
                    Updated {new Date(rule.updatedAt).toLocaleDateString()} • by {rule.author}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Tabs.Content>

        <Tabs.Content value="about" className="space-y-6">
          <Card className="space-y-4">
            <div>
              <div className="font-semibold">Text Readability Improvements</div>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Issues Fixed:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Improved text contrast ratios for better readability</li>
                <li>Enhanced card backgrounds to separate from main background</li>
                <li>Fixed muted text colors that were too faint</li>
                <li>Added proper borders and visual hierarchy</li>
                <li>Ensured WCAG accessibility compliance</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Technical Changes:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Updated CSS color variables for dark theme</li>
                <li>Enhanced Card, Input, Tabs, and other UI components</li>
                <li>Added explicit text color classes throughout</li>
                <li>Improved visual separation with borders and shadows</li>
              </ul>
            </div>
          </Card>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
}
