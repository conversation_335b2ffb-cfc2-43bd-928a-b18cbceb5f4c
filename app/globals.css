@import "@radix-ui/themes/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Radix UI Themes integration */
.radix-themes {
  --default-font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  font-family: var(--default-font-family);
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 8%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 8%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 75%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Force dark background and white text */
  html, body {
    background-color: #0a0a0a !important;
    color: #ffffff !important;
  }

  /* Force all containers to have proper background */
  .container, main, div {
    background-color: transparent !important;
  }

  /* Ensure Radix UI theme takes precedence */
  .radix-themes {
    color: var(--gray-12) !important;
  }

  /* Override Tailwind text colors to use Radix UI tokens */
  .text-foreground {
    color: var(--gray-12) !important;
  }

  .text-muted-foreground {
    color: var(--gray-11) !important;
  }

  /* Card styling using Radix UI tokens */
  .bg-card {
    background-color: var(--color-panel-solid) !important;
  }

  .text-card-foreground {
    color: var(--gray-12) !important;
  }
}

/* EMERGENCY TEXT VISIBILITY FIX */
* {
  color: #ffffff !important;
}

.text-muted-foreground {
  color: #cccccc !important;
}

/* Custom utilities for Radix UI integration */
@layer utilities {
  .radix-themes-custom {
    /* Ensure proper spacing and typography inheritance */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
  }

  /* Error state styling using theme tokens */
  .error-border {
    border-color: var(--red-9);
  }

  .error-text {
    color: var(--red-11);
  }

  /* Focus states using theme tokens */
  .focus-ring {
    outline: 2px solid var(--accent-8);
    outline-offset: 2px;
  }

  /* Force high contrast text for all elements */
  .force-high-contrast * {
    color: var(--gray-12) !important;
  }

  .force-high-contrast .text-muted-foreground {
    color: var(--gray-11) !important;
  }

  /* Ensure buttons and interactive elements are visible */
  button, .btn {
    color: var(--gray-12) !important;
  }

  /* Ensure all headings are visible */
  h1, h2, h3, h4, h5, h6 {
    color: var(--gray-12) !important;
  }

  /* Ensure all text elements are visible with maximum specificity */
  html body p,
  html body span,
  html body div,
  html body label,
  html body h1,
  html body h2,
  html body h3,
  html body h4,
  html body h5,
  html body h6 {
    color: #ffffff !important;
  }

  /* Override for muted text with high contrast */
  html body .text-muted-foreground,
  html body [class*="muted"] {
    color: #cccccc !important;
  }

  /* Force white text on all interactive elements */
  html body button,
  html body .btn,
  html body input,
  html body textarea,
  html body select {
    color: #ffffff !important;
  }
}

/* Fix for Radix UI Dropdown and Dialog visibility */
.rt-DropdownMenuContent,
.rt-DialogContent,
.rt-PopoverContent,
.rt-SelectContent,
.rt-ContextMenuContent {
  background-color: var(--color-panel-solid) !important;
  border: 1px solid var(--gray-6) !important;
}

/* Ensure proper text contrast in Radix UI components */
.rt-DropdownMenuContent *,
.rt-DialogContent *,
.rt-PopoverContent *,
.rt-SelectContent *,
.rt-ContextMenuContent * {
  color: var(--gray-12) !important;
}

/* Specific fixes for dropdown menu items */
.rt-DropdownMenuItem {
  color: var(--gray-12) !important;
}

.rt-DropdownMenuItem:hover,
.rt-DropdownMenuItem:focus {
  background-color: var(--gray-3) !important;
  color: var(--gray-12) !important;
}

/* Dialog specific fixes */
.rt-DialogTitle {
  color: var(--gray-12) !important;
}

.rt-DialogDescription {
  color: var(--gray-11) !important;
}

/* Ensure separators are visible */
.rt-DropdownMenuSeparator {
  background-color: var(--gray-6) !important;
}

/* Additional fixes for better visibility */
/* Radix Portal elements (dropdowns and modals render in portals) */
[data-radix-popper-content-wrapper] {
  z-index: 50;
}

/* Radix Dropdown specific */
[role="menu"] {
  background-color: var(--gray-2) !important;
  border: 1px solid var(--gray-6) !important;
  border-radius: var(--radius) !important;
  padding: 4px !important;
  box-shadow: 0 10px 38px -10px rgba(0, 0, 0, 0.35), 0 10px 20px -15px rgba(0, 0, 0, 0.2) !important;
}

[role="menuitem"] {
  color: var(--gray-12) !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  outline: none !important;
}

[role="menuitem"]:hover,
[role="menuitem"]:focus {
  background-color: var(--gray-4) !important;
}

/* Radix Dialog specific */
[role="dialog"] {
  background-color: var(--gray-2) !important;
  border: 1px solid var(--gray-6) !important;
  border-radius: var(--radius) !important;
  color: var(--gray-12) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;
}

[role="dialog"] * {
  color: inherit !important;
}

/* Fix overlay for dialogs */
[data-radix-dialog-overlay],
[data-radix-dismissable-layer] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Ensure all text in portals is readable */
[data-radix-portal] * {
  color: var(--gray-12) !important;
}

/* Override the emergency white text for Radix components */
.rt-Theme [role="menu"] *,
.rt-Theme [role="dialog"] *,
.rt-Theme [data-radix-portal] * {
  color: var(--gray-12) !important;
}

/* Muted text in Radix components should be slightly dimmer */
.rt-Theme [role="menu"] .text-muted-foreground,
.rt-Theme [role="dialog"] .text-muted-foreground {
  color: var(--gray-11) !important;
  opacity: 0.8 !important;
}

/* Fix native select and form elements */
select,
select option {
  background-color: var(--gray-2) !important;
  color: var(--gray-12) !important;
  border: 1px solid var(--gray-6) !important;
}

select:focus {
  outline: 2px solid var(--accent-8) !important;
  outline-offset: 2px !important;
}

/* Ensure input fields maintain readability */
input:not([type="checkbox"]):not([type="radio"]),
textarea {
  background-color: var(--gray-2) !important;
  color: var(--gray-12) !important;
  border: 1px solid var(--gray-6) !important;
}

input:not([type="checkbox"]):not([type="radio"]):focus,
textarea:focus {
  outline: 2px solid var(--accent-8) !important;
  outline-offset: 2px !important;
  border-color: var(--accent-8) !important;
}

/* Fix placeholder text */
input::placeholder,
textarea::placeholder {
  color: var(--gray-10) !important;
  opacity: 0.7 !important;
}
