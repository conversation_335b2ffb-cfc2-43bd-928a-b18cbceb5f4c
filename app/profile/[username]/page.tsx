import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { Ava<PERSON>, Button } from "@radix-ui/themes";
import { RuleCard } from "@/components/rule-card";
import { 
  Calendar, 
  Code, 
  Globe, 
  Github,
  Twitter,
  Link as LinkIcon
} from "lucide-react";
import Link from "next/link";
import prisma from "@/lib/prisma";

interface PageProps {
  params: {
    username: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id: params.username
      }
    });

    if (!user) {
      return {
        title: "User Not Found",
        description: "The requested user could not be found."
      };
    }

    const title = `${user.name || "Anonymous"} - OnlyRules Profile`;
    const description = `View all public AI prompt rules created by ${user.name || "Anonymous"} on OnlyRules`;

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        url: `https://onlyrules.app/profile/${user.id}`,
        siteName: "OnlyRules",
        type: "profile",
      },
      twitter: {
        card: "summary",
        title,
        description,
      },
    };
  } catch (error) {
    console.warn('Failed to generate profile metadata:', error);
    return {
      title: "OnlyRules Profile",
      description: "User profile on OnlyRules"
    };
  }
}

export default async function ProfilePage({ params }: PageProps) {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id: params.username
      },
      include: {
        _count: {
          select: {
            rules: {
              where: { visibility: "PUBLIC" }
            }
          }
        }
      }
    });

    if (!user) {
      notFound();
    }

    // Get all public rules by this user
    const rules = await prisma.rule.findMany({
      where: {
        userId: user.id,
        visibility: "PUBLIC"
      },
      include: {
        tags: {
          include: {
            tag: true
          }
        },
        user: true
      },
      orderBy: { updatedAt: "desc" }
    });

    // Get statistics
    const stats = await prisma.rule.groupBy({
      by: ['ideType'],
      where: {
        userId: user.id,
        visibility: "PUBLIC"
      },
      _count: true
    });

    const totalTags = await prisma.ruleTag.findMany({
      where: {
        rule: {
          userId: user.id,
          visibility: "PUBLIC"
        }
      },
      select: {
        tag: true
      },
      distinct: ['tagId']
    });

    return (
    <div className="container max-w-6xl py-8">
      {/* Profile Header */}
      <div className="mb-8">
        <div className="flex items-start gap-6 mb-6">
          <Avatar 
            className="h-24 w-24"
            src={user.image || undefined}
            fallback={user.name?.charAt(0) || "A"}
          />
          
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">{user.name || "Anonymous"}</h1>

            
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Joined {new Date(user.createdAt).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-1">
                <Globe className="h-4 w-4" />
                <span>{user._count.rules} public rules</span>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            {/* Add social links if available in user model */}

          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-card border rounded-lg p-4">
            <div className="text-2xl font-bold">{rules.length}</div>
            <div className="text-sm text-muted-foreground">Public Rules</div>
          </div>
          <div className="bg-card border rounded-lg p-4">
            <div className="text-2xl font-bold">{stats.length}</div>
            <div className="text-sm text-muted-foreground">IDE Types</div>
          </div>
          <div className="bg-card border rounded-lg p-4">
            <div className="text-2xl font-bold">{totalTags.length}</div>
            <div className="text-sm text-muted-foreground">Unique Tags</div>
          </div>
        </div>
      </div>

      {/* Rules Section */}
      <div>
        <h2 className="text-2xl font-semibold mb-6">Public Rules</h2>
        
        {rules.length === 0 ? (
          <div className="text-center py-12">
            <Code className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No public rules yet</p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {rules.map((rule) => (
              <RuleCard 
                key={rule.id} 
                rule={{
                  ...rule,
                  createdAt: rule.createdAt.toISOString(),
                  updatedAt: rule.updatedAt.toISOString()
                }} 
              />
            ))}
          </div>
        )}
      </div>

      {/* IDE Type Breakdown */}
      {stats.length > 0 && (
        <div className="mt-12">
          <h2 className="text-2xl font-semibold mb-6">IDE Specialization</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {stats.map((stat) => (
              <div key={stat.ideType} className="bg-card border rounded-lg p-4 text-center">
                <div className="text-lg font-semibold">{stat._count}</div>
                <div className="text-sm text-muted-foreground">{stat.ideType}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
  } catch (error) {
    console.error('Error loading profile:', error);
    notFound();
  }
}