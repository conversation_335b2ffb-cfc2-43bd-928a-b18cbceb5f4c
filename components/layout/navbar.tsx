"use client";

import Link from "next/link";
import { useState } from "react";
import { useTheme } from "next-themes";
import { <PERSON>, Sun, Code, User, LogOut, Settings, Github } from "lucide-react";
import { Button, DropdownMenu, Avatar } from "@radix-ui/themes";
import { useSession, signOut } from "@/lib/auth-client";
import { LanguageSwitcher } from "@/components/ui/language-switcher";
import { IDEPreferenceManager } from "@/components/ide-preferences/ide-preference-manager";
import { type Locale } from "@/lib/i18n";
// import { useLingui } from '@lingui/react';

interface NavbarProps {
  locale: Locale;
}

export function Navbar({ locale }: NavbarProps) {
  const { theme, setTheme } = useTheme();
  const { data: session } = useSession();
  const [showIDEPreferences, setShowIDEPreferences] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  // const { i18n } = useLingui();

  const handleSignOut = async () => {
    await signOut();
    setDropdownOpen(false);
  };

  const handleIDEPreferences = () => {
    setShowIDEPreferences(true);
    setDropdownOpen(false);
  };

  const handleNavigation = () => {
    setDropdownOpen(false);
  };

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center gap-2">
            <Code className="h-6 w-6 text-primary" />
            <span className="font-bold text-xl">OnlyRules</span>
          </Link>
          
          <nav className="hidden md:flex items-center gap-6">
            <Link
              href="/dashboard"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Dashboard
            </Link>
            <Link
              href="/demo"
              className="text-sm font-medium transition-colors hover:text-primary text-blue-600 dark:text-blue-400"
            >
              Demo
            </Link>
            <Link
              href="/dashboard-test"
              className="text-sm font-medium transition-colors hover:text-primary text-orange-600 dark:text-orange-400"
            >
              Dashboard Test
            </Link>
            <Link
              href="/templates"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Templates
            </Link>
            <Link
              href="/ides"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              IDEs
            </Link>
            <Link
              href="/tutorials"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Tutorials
            </Link>
            <Link
              href="/shared"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Community
            </Link>
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <LanguageSwitcher currentLocale={locale} />
          
          <Button variant="ghost" size="2" asChild>
            <Link
              href="https://github.com/ranglang/onlyrules"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              <Github className="h-4 w-4" />
              <span className="hidden sm:inline">GitHub</span>
            </Link>
          </Button>

          <Button
            variant="ghost"
            size="2"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>

          {session?.user ? (
            <DropdownMenu.Root open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenu.Trigger>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar
                    className="h-8 w-8"
                    src={session.user.image || ""}
                    fallback={session.user.name?.charAt(0) || session.user.email?.charAt(0) || "U"}
                  />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content className="w-56" align="end">
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    {session.user.name && (
                      <p className="font-medium">{session.user.name}</p>
                    )}
                    {session.user.email && (
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {session.user.email}
                      </p>
                    )}
                  </div>
                </div>
                <DropdownMenu.Separator />
                <DropdownMenu.Item asChild>
                  <Link href="/dashboard" onClick={handleNavigation}>
                    <User className="mr-2 h-4 w-4" />
                    Dashboard
                  </Link>
                </DropdownMenu.Item>
                <DropdownMenu.Item onClick={handleIDEPreferences}>
                  <Code className="mr-2 h-4 w-4" />
                  IDE Preferences
                </DropdownMenu.Item>
                <DropdownMenu.Item asChild>
                  <Link href="/settings" onClick={handleNavigation}>
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Link>
                </DropdownMenu.Item>
                <DropdownMenu.Separator />
                <DropdownMenu.Item onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          ) : (
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="2">
                <Link href="/auth/signin">Sign In</Link>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* IDE Preferences Dialog */}
      <IDEPreferenceManager
        open={showIDEPreferences}
        onOpenChange={setShowIDEPreferences}
      />
    </nav>
  );
}